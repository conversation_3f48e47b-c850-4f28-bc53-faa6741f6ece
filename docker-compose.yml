services:
  db:
    image: postgres:latest
    container_name: hex-erp-db
    environment:
      POSTGRES_USER: odoo
      POSTGRES_PASSWORD: odoo
      POSTGRES_DB: postgres
    volumes:
      - odoo-db:/var/lib/postgresql/data
    ports:
      - '5432:5432'
    restart: unless-stopped

  odoo:
    image: odoo:17
    container_name: hex-erp-odoo
    depends_on:
      - db
    environment:
      - HOST=db
      - USER=odoo
      - PASSWORD=odoo
    volumes:
      # 挂载整个项目目录
      - .:/mnt/extra-addons
      # 挂载Odoo配置文件
      - ./config/odoo.conf:/etc/odoo/odoo.conf
      # Odoo数据目录
      - odoo-data:/var/lib/odoo
    ports:
      - '8069:8069'
      - '8071:8071' # 长轮询端口
      - '8072:8072' # 聊天端口
    restart: unless-stopped
    command: odoo --dev=reload,qweb,werkzeug,xml

volumes:
  odoo-db:
    external: true
  odoo-data:
