[options]
# 数据库配置
db_host = db
db_port = 5432
db_user = odoo
db_password = odoo
db_name = False

# 插件路径配置 - 包含所有插件目录
addons_path = /usr/lib/python3/dist-packages/odoo/addons,/var/lib/odoo/addons/17.0,/mnt/extra-addons/buy-addons,/mnt/extra-addons/enterprise-addons,/mnt/extra-addons/free-addons,/mnt/extra-addons/my-addons

# 服务器配置
http_port = 8069
longpolling_port = 8072

# 开发模式配置
log_level = debug
log_handler = :DEBUG
workers = 0
max_cron_threads = 1

# 安全配置
admin_passwd = admin123
list_db = True

# 性能配置
limit_memory_hard = 2684354560
limit_memory_soft = 2147483648
limit_request = 8192
limit_time_cpu = 600
limit_time_real = 1200

# 文件上传配置
data_dir = /var/lib/odoo

# 开发者选项
dev_mode = reload,qweb,werkzeug,xml
test_enable = False
test_file = False
test_tags = False

# 邮件配置（开发环境）
email_from = False
smtp_server = localhost
smtp_port = 25
smtp_ssl = False
smtp_user = False
smtp_password = False
