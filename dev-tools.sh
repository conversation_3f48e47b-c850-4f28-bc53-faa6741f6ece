#!/bin/bash

# Hex-ERP 开发工具脚本

case "$1" in
    "start")
        echo "启动开发环境..."
        docker-compose up -d
        echo "开发环境已启动！"
        echo "访问地址: http://localhost:8069/web?db=hex_erp_dev"
        ;;
    "stop")
        echo "停止开发环境..."
        docker-compose down
        echo "开发环境已停止！"
        ;;
    "restart")
        echo "重启Odoo服务..."
        docker-compose restart odoo
        echo "Odoo服务已重启！"
        ;;
    "logs")
        echo "查看Odoo日志..."
        docker-compose logs -f odoo
        ;;
    "shell")
        echo "进入Odoo容器..."
        docker-compose exec odoo bash
        ;;
    "db")
        echo "连接数据库..."
        docker-compose exec db psql -U odoo -d hex_erp_dev
        ;;
    "install")
        if [ -z "$2" ]; then
            echo "用法: $0 install <模块名>"
            exit 1
        fi
        echo "安装模块: $2"
        docker-compose exec odoo odoo -d hex_erp_dev -i $2 --stop-after-init
        echo "模块 $2 安装完成！"
        ;;
    "upgrade")
        if [ -z "$2" ]; then
            echo "用法: $0 upgrade <模块名>"
            exit 1
        fi
        echo "升级模块: $2"
        docker-compose exec odoo odoo -d hex_erp_dev -u $2 --stop-after-init
        echo "模块 $2 升级完成！"
        ;;
    "backup")
        BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
        echo "备份数据库到: $BACKUP_FILE"
        docker-compose exec db pg_dump -U odoo hex_erp_dev > $BACKUP_FILE
        echo "数据库备份完成！"
        ;;
    "status")
        echo "检查服务状态..."
        docker-compose ps
        ;;
    *)
        echo "Hex-ERP 开发工具"
        echo ""
        echo "用法: $0 <命令> [参数]"
        echo ""
        echo "可用命令:"
        echo "  start          启动开发环境"
        echo "  stop           停止开发环境"
        echo "  restart        重启Odoo服务"
        echo "  logs           查看Odoo日志"
        echo "  shell          进入Odoo容器"
        echo "  db             连接数据库"
        echo "  install <模块>  安装模块"
        echo "  upgrade <模块>  升级模块"
        echo "  backup         备份数据库"
        echo "  status         查看服务状态"
        echo ""
        echo "开发数据库: hex_erp_dev"
        echo "访问地址: http://localhost:8069/web?db=hex_erp_dev"
        ;;
esac
