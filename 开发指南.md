# Hex-ERP 本地开发环境指南

## 环境概述

你的本地开发环境现在已经配置完成，包含：

- **数据库**: PostgreSQL 17 (容器名: hex-erp-db, 端口: 5432)
- **Odoo**: Odoo 17 (容器名: hex-erp-odoo, 端口: 8069)
- **插件目录**: 所有插件目录都已挂载并配置

## 访问地址

- **Odoo Web 界面**: http://localhost:8069/web?db=hex_erp_dev
- **开发数据库**: hex_erp_dev (全新的 Odoo 17 数据库)
- **数据库连接**: localhost:5432
  - 用户名: odoo
  - 密码: odoo
  - 开发数据库名: hex_erp_dev

## 快速开始

使用提供的开发工具脚本：

```bash
# 查看所有可用命令
./dev-tools.sh

# 启动开发环境
./dev-tools.sh start

# 查看日志
./dev-tools.sh logs

# 安装模块
./dev-tools.sh install module_name
```

## 常用命令

### 启动/停止服务

```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启Odoo服务
docker-compose restart odoo

# 查看服务状态
docker-compose ps
```

### 查看日志

```bash
# 查看Odoo日志
docker-compose logs odoo

# 实时查看日志
docker-compose logs -f odoo

# 查看最近20行日志
docker-compose logs odoo --tail 20
```

### 进入容器

```bash
# 进入Odoo容器
docker-compose exec odoo bash

# 进入数据库容器
docker-compose exec db psql -U odoo -d postgres
```

## 开发工作流程

### 1. 修改现有模块

1. 直接编辑项目目录中的文件
2. 保存后 Odoo 会自动重载（开发模式已启用）
3. 刷新浏览器查看效果

### 2. 创建新模块

1. 在 `my-addons` 目录下创建新模块
2. 重启 Odoo 服务：`docker-compose restart odoo`
3. 在 Odoo 界面中安装新模块

### 3. 安装/升级模块

```bash
# 进入Odoo容器
docker-compose exec odoo bash

# 安装模块
odoo -d odoo17 -i module_name --stop-after-init

# 升级模块
odoo -d odoo17 -u module_name --stop-after-init
```

### 4. 数据库操作

```bash
# 备份数据库
docker-compose exec db pg_dump -U odoo odoo17 > backup.sql

# 恢复数据库
docker-compose exec -T db psql -U odoo -d odoo17 < backup.sql
```

## 插件目录说明

- **my-addons**: 自定义开发的模块（主要开发目录）
- **buy-addons**: 购买的第三方模块
- **enterprise-addons**: Odoo 企业版模块
- **free-addons**: 免费的第三方模块

## 开发模式功能

当前环境已启用以下开发功能：

- 代码自动重载
- 详细调试日志
- QWeb 模板调试
- Werkzeug 调试器

## 故障排除

### 如果 Odoo 无法启动

1. 检查日志：`docker-compose logs odoo`
2. 检查配置文件：`config/odoo.conf`
3. 重启服务：`docker-compose restart odoo`

### 如果模块无法加载

1. 检查模块路径是否正确
2. 检查 `__manifest__.py` 文件
3. 重启 Odoo 并查看日志

### 如果数据库连接失败

1. 确保数据库容器正在运行：`docker-compose ps`
2. 检查数据库日志：`docker-compose logs db`

## 性能优化建议

1. 开发时只安装必要的模块
2. 定期清理日志文件
3. 使用 SSD 存储提高性能
4. 根据需要调整内存限制

## 下一步

现在你可以：

1. 访问 http://localhost:8069 开始使用 Odoo
2. 选择一个模块开始开发
3. 使用上述命令进行日常开发工作

如果需要开发特定模块，请告诉我模块名称，我可以提供更详细的指导。
